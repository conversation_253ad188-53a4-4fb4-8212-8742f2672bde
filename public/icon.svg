<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#grad1)" rx="64"/>
  <text x="256" y="180" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">Career</text>
  <text x="256" y="240" font-family="Arial, sans-serif" font-size="36" font-weight="bold" text-anchor="middle" fill="white">Jumpstart</text>
  <text x="256" y="300" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white">Hub</text>
  <circle cx="256" cy="380" r="40" fill="white" opacity="0.8"/>
  <path d="M236 370 L246 380 L276 350" stroke="#4F46E5" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
